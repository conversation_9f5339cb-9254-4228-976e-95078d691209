import 'package:gp_core/core.dart';

/// Enum representing the add follower options
enum AddFollowerOption {
  /// Add to current step only
  currentStep,

  /// Add to all steps
  allSteps,
}

extension AddFollowerOptionExt on AddFollowerOption {
  /// Display name for the option
  String get displayName {
    switch (this) {
      case AddFollowerOption.currentStep:
        return "Thêm vào bước hiện tại";
      case AddFollowerOption.allSteps:
        return "Thêm vào tất cả các bước";
    }
  }

  /// Description for the option
  String get description {
    switch (this) {
      case AddFollowerOption.currentStep:
        return "Người theo dõi sẽ chỉ được thêm vào bước đang chọn trong quy trình";
      case AddFollowerOption.allSteps:
        return "Người theo dõi sẽ được thêm vào tất cả các bước, kể cả đã hoàn thành và sắp tới";
    }
  }

  /// Icon asset for the option
  String get iconAsset {
    switch (this) {
      case AddFollowerOption.currentStep:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_ADD_SVG;
      case AddFollowerOption.allSteps:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_FOLDER_TREE_SVG;
    }
  }
}

/// Result class for add follower bottom sheet
class AddFollowerBottomSheetResult {
  const AddFollowerBottomSheetResult({
    required this.option,
  });

  final AddFollowerOption option;
}
