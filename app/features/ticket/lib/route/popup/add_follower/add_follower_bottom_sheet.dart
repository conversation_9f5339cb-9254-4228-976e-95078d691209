// ignore_for_file: public_member_api_docs

/*
 * Created Date: Thursday, 12th December 2024, 00:00:00
 * Author: AI Assistant
 * -----
 * Last Modified: Thursday, 12th December 2024 00:00:00
 * Modified By: AI Assistant
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

import '../../../domain/entity/enums/ticket/action/ticket_action.popup.dart';
import 'add_follower_option.dart';

class AddFollowerBottomSheet extends StatelessWidget {
  const AddFollowerBottomSheet({
    super.key,
    required this.entity,
  });

  final TicketActionBottomSheetEntity entity;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          _buildHeader(context),

          // Options
          _buildOptions(context),

          // Bottom safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      height: 44,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: GPColor.bgPrimary,
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF2F3136).withValues(alpha: 0.1),
            offset: const Offset(0, 1),
            blurRadius: 1,
          ),
        ],
      ),
      child: Row(
        children: [
          const SizedBox(width: 48), // Space for alignment
          Expanded(
            child: Text(
              "Thêm người theo dõi",
              textAlign: TextAlign.center,
              style: textStyle(GPTypography.headingMedium)?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                height: 1.5,
                color: GPColor.contentPrimary,
              ),
            ),
          ),
          InkWell(
            onTap: () => Navigator.of(context).pop(),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 48,
              height: 44,
              padding: const EdgeInsets.all(12),
              child: SvgWidget(
                Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_XMARK_SVG,
                color: GPColor.contentPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptions(BuildContext context) {
    return Container(
      color: GPColor.bgPrimary,
      child: Column(
        children: [
          // Current step option
          _buildOptionItem(
            context,
            AddFollowerOption.currentStep,
          ),

          // Divider
          Container(
            height: 1,
            margin: const EdgeInsets.only(left: 64),
            color: GPColor.lineSecondary,
          ),

          // All steps option
          _buildOptionItem(
            context,
            AddFollowerOption.allSteps,
          ),
        ],
      ),
    );
  }

  Widget _buildOptionItem(BuildContext context, AddFollowerOption option) {
    return InkWell(
      onTap: () {
        Navigator.of(context).pop(
          AddFollowerBottomSheetResult(option: option),
        );
      },
      child: Container(
        padding: const EdgeInsets.fromLTRB(24, 20, 8, 20),
        child: Row(
          children: [
            // Icon
            SvgWidget(
              option.iconAsset,
              width: 24,
              height: 24,
              color: GPColor.contentPrimary,
            ),

            const SizedBox(width: 16),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    option.displayName,
                    style: textStyle(GPTypography.bodyMedium)?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      height: 1.5,
                      color: GPColor.contentPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    option.description,
                    style: textStyle(GPTypography.bodyMedium)?.copyWith(
                      fontWeight: FontWeight.w400,
                      fontSize: 16,
                      height: 1.5,
                      color: GPColor.contentSecondary,
                    ),
                  ),
                ],
              ),
            ),

            // Chevron right
            SvgWidget(
              Assets
                  .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_CHEVRON_RIGHT_SVG,
              width: 24,
              height: 24,
              color: GPColor.contentSecondary,
            ),
          ],
        ),
      ),
    );
  }
}
